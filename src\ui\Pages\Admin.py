"""
Admin Dashboard for TeamLogic-AutoTask
Provides administrative functions and system overview.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta, date
from collections import Counter
from typing import List, Dict
import random

# Import shared UI components
from src.ui.components import (
    apply_custom_css, create_metric_card, create_status_badge, 
    create_header_section, create_footer_section, format_time_elapsed,
    format_date_display, create_priority_icon, create_data_table,
    create_chart_container, create_filter_section
)

# Configuration Constants
PAGE_TITLE = "TeamLogic-AutoTask - Admin Dashboard"
LAYOUT = "wide"
PAGE_ICON = "👨‍💼"
PRIORITY_OPTIONS = ["Low", "Medium", "High", "Critical", "Desktop/User Down"]
STATUS_OPTIONS = ["Open", "Assigned", "In Progress", "Pending Parts", "Resolved", "Closed"]
TECHNICIAN_STATUS = ["Available", "Busy", "On Break", "Off Duty"]
DEPARTMENTS = ["IT", "HR", "Finance", "Marketing", "Operations", "Sales"]
SUPPORT_PHONE = "9723100860"
SUPPORT_EMAIL = "<EMAIL>"

def create_enhanced_admin_header(admin_name: str, admin_id: str):
    """Create an enhanced header section for admin dashboard."""
    current_time = datetime.now().strftime("%H:%M")
    current_theme = st.session_state.get('theme', 'dark')
    theme_icon = "🌙" if current_theme == 'dark' else "☀️"
    
    create_header_section(
        title=f"👨‍💼 {PAGE_TITLE}",
        subtitle=f"Welcome, {admin_name}! Monitor and manage the entire support system."
    )
    
    # Admin info card
    st.markdown(f"""
    <div class="user-info-section">
        <h4>👨‍💼 Administrator Information</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
            <div>
                <strong>Name:</strong> {admin_name}<br>
                <strong>ID:</strong> {admin_id}<br>
                <strong>Role:</strong> System Administrator
            </div>
            <div>
                <strong>Current Time:</strong> {current_time}<br>
                <strong>Theme:</strong> {theme_icon} {current_theme.title()}<br>
                <strong>Access Level:</strong> Full Access
            </div>
            <div>
                <strong>Last Login:</strong> Today, 08:30 AM<br>
                <strong>Session:</strong> Active<br>
                <strong>Permissions:</strong> All Systems
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def apply_admin_css():
    """Apply admin-specific CSS styling."""
    st.markdown("""
    <style>
    .admin-card {
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        border: 1px solid var(--border-color);
        border-left: 4px solid var(--primary);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    .system-status-good {
        background-color: rgba(40, 167, 69, 0.1);
        border: 2px solid var(--success);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .system-status-warning {
        background-color: rgba(255, 193, 7, 0.1);
        border: 2px solid var(--warning);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .system-status-critical {
        background-color: rgba(220, 53, 69, 0.1);
        border: 2px solid var(--danger);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .admin-action-btn {
        margin: 5px;
        padding: 10px 20px;
        border-radius: 5px;
        border: none;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-manage { background-color: var(--primary); color: white; }
    .btn-report { background-color: var(--info); color: white; }
    .btn-settings { background-color: var(--warning); color: black; }
    .btn-emergency { background-color: var(--danger); color: white; }
    
    .department-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }
    
    .technician-performance {
        background-color: var(--accent);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border: 1px solid var(--border-color);
    }
    </style>
    """, unsafe_allow_html=True)

def apply_custom_css():
    """Apply custom CSS styling for admin dashboard."""
    # Import and use the shared apply_custom_css from components
    from src.ui.components import apply_custom_css as shared_apply_css
    shared_apply_css()
    # Add admin-specific styles
    apply_admin_css()

def generate_system_data():
    """Generate mock system data for admin dashboard."""
    return {
        "total_tickets": random.randint(150, 300),
        "open_tickets": random.randint(20, 50),
        "in_progress": random.randint(15, 35),
        "resolved_today": random.randint(25, 60),
        "avg_resolution_time": round(random.uniform(2.5, 8.5), 1),
        "technician_count": random.randint(8, 15),
        "active_technicians": random.randint(6, 12),
        "user_count": random.randint(200, 500),
        "departments": DEPARTMENTS,
        "system_uptime": "99.8%",
        "database_status": "Healthy",
        "api_response_time": f"{random.randint(50, 200)}ms"
    }

def generate_technician_data():
    """Generate mock technician performance data."""
    technicians = []
    for i in range(1, 11):
        tech = {
            "id": f"TECH-{i:03d}",
            "name": f"Technician {i}",
            "status": random.choice(TECHNICIAN_STATUS),
            "assigned_tickets": random.randint(3, 12),
            "completed_today": random.randint(1, 8),
            "avg_resolution_time": round(random.uniform(1.5, 6.0), 1),
            "specialization": random.choice(["Hardware", "Software", "Network", "Security"]),
            "performance_score": random.randint(75, 98)
        }
        technicians.append(tech)
    return technicians

def generate_department_data():
    """Generate mock department ticket data."""
    dept_data = []
    for dept in DEPARTMENTS:
        data = {
            "department": dept,
            "total_tickets": random.randint(10, 50),
            "open_tickets": random.randint(2, 15),
            "avg_resolution_time": round(random.uniform(2.0, 8.0), 1),
            "satisfaction_score": round(random.uniform(3.5, 5.0), 1)
        }
        dept_data.append(data)
    return dept_data

def dashboard_page():
    """Main admin dashboard page with system overview."""
    # Enhanced header
    admin_name = st.session_state.get('admin_name', 'David Brown')
    admin_id = st.session_state.get('admin_id', 'ADMIN-001')
    
    create_enhanced_admin_header(admin_name, admin_id)
    
    # System data
    system_data = generate_system_data()
    
    # System Overview Metrics
    st.markdown("### 📊 System Overview")
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        create_metric_card("Total Tickets", str(system_data["total_tickets"]))
    with col2:
        create_metric_card("Open Tickets", str(system_data["open_tickets"]))
    with col3:
        create_metric_card("In Progress", str(system_data["in_progress"]))
    with col4:
        create_metric_card("Resolved Today", str(system_data["resolved_today"]))
    with col5:
        create_metric_card("Avg Resolution", f"{system_data['avg_resolution_time']}h")
    
    # System Health Status
    st.markdown("### 🏥 System Health")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="system-status-good">
            <h4>🟢 Database Status</h4>
            <p><strong>Status:</strong> Healthy</p>
            <p><strong>Uptime:</strong> 99.8%</p>
            <p><strong>Response Time:</strong> 45ms</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="system-status-good">
            <h4>🟢 API Services</h4>
            <p><strong>Status:</strong> Operational</p>
            <p><strong>Response Time:</strong> 120ms</p>
            <p><strong>Success Rate:</strong> 99.5%</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="system-status-warning">
            <h4>🟡 Storage Usage</h4>
            <p><strong>Status:</strong> Monitoring</p>
            <p><strong>Usage:</strong> 78% of 1TB</p>
            <p><strong>Estimated Full:</strong> 45 days</p>
        </div>
        """, unsafe_allow_html=True)

def technician_management_page():
    """Technician management and performance page."""
    st.markdown("### 👨‍🔧 Technician Management")
    
    # Technician overview metrics
    tech_data = generate_technician_data()
    active_techs = len([t for t in tech_data if t["status"] == "Available"])
    busy_techs = len([t for t in tech_data if t["status"] == "Busy"])
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        create_metric_card("Total Technicians", str(len(tech_data)))
    with col2:
        create_metric_card("Available", str(active_techs))
    with col3:
        create_metric_card("Busy", str(busy_techs))
    with col4:
        avg_performance = sum(t["performance_score"] for t in tech_data) / len(tech_data)
        create_metric_card("Avg Performance", f"{avg_performance:.1f}%")
    
    # Technician performance table
    st.markdown("### 📊 Technician Performance")
    df = pd.DataFrame(tech_data)
    st.dataframe(df, use_container_width=True)

def system_settings_page():
    """System settings and configuration page."""
    st.markdown("### ⚙️ System Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🔧 General Settings")
        auto_assign = st.checkbox("Auto-assign tickets", value=True)
        email_notifications = st.checkbox("Email notifications", value=True)
        sms_alerts = st.checkbox("SMS alerts for critical tickets", value=False)
        
        st.markdown("#### 📧 Notification Settings")
        notification_frequency = st.selectbox(
            "Notification Frequency",
            ["Immediate", "Every 15 minutes", "Hourly", "Daily"]
        )
    
    with col2:
        st.markdown("#### 🎯 Performance Targets")
        target_resolution_time = st.number_input("Target Resolution Time (hours)", value=4.0, step=0.5)
        target_satisfaction = st.number_input("Target Satisfaction Score", value=4.5, step=0.1)
        
        st.markdown("#### 🔒 Security Settings")
        session_timeout = st.number_input("Session Timeout (minutes)", value=30, step=5)
        password_policy = st.selectbox("Password Policy", ["Standard", "Strong", "Very Strong"])

def main():
    """Main application entry point for admin dashboard."""
    st.set_page_config(
        page_title=PAGE_TITLE,
        layout=LAYOUT,
        page_icon=PAGE_ICON,
        initial_sidebar_state="expanded"
    )

    # Initialize session state
    if "page" not in st.session_state:
        st.session_state.page = "dashboard"
    if "admin_name" not in st.session_state:
        st.session_state.admin_name = "David Brown"
    if "admin_id" not in st.session_state:
        st.session_state.admin_id = "ADMIN-001"
    if "theme" not in st.session_state:
        st.session_state.theme = "dark"

    # Apply custom CSS
    apply_custom_css()

    # Create sidebar (will be enhanced later)
    with st.sidebar:
        st.markdown("### 👨‍💼 Admin Navigation")
        
        if st.button("🏠 Dashboard", key="nav_dashboard", use_container_width=True):
            st.session_state.page = "dashboard"
            st.rerun()
        
        if st.button("👨‍🔧 Technicians", key="nav_technicians", use_container_width=True):
            st.session_state.page = "technicians"
            st.rerun()
        
        if st.button("⚙️ Settings", key="nav_settings", use_container_width=True):
            st.session_state.page = "settings"
            st.rerun()

    # Route to appropriate page
    if st.session_state.page == "dashboard":
        dashboard_page()
    elif st.session_state.page == "technicians":
        technician_management_page()
    elif st.session_state.page == "settings":
        system_settings_page()

if __name__ == "__main__":
    main()

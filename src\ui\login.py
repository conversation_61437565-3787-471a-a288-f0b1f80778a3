"""
Login System for TeamLogic-AutoTask
Handles user authentication and role-based routing.
"""

import streamlit as st
import hashlib
from typing import Dict, Optional

# Mock user database (in production, this would be from a real database)
USERS_DB = {
    "user1": {
        "password": "password123",  # In production, this would be hashed
        "role": "user",
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "******-123-4567",
        "company_id": "COMP-001",
        "department": "IT Department"
    },
    "user2": {
        "password": "password123",
        "role": "user", 
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "******-123-4568",
        "company_id": "COMP-001",
        "department": "Marketing"
    },
    "tech1": {
        "password": "tech123",
        "role": "technician",
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "******-123-4569",
        "tech_id": "TECH-001",
        "specialization": "Hardware"
    },
    "tech2": {
        "password": "tech123",
        "role": "technician",
        "name": "<PERSON>",
        "email": "<EMAIL>", 
        "phone": "******-123-4570",
        "tech_id": "TECH-002",
        "specialization": "Software"
    },
    "admin1": {
        "password": "admin123",
        "role": "admin",
        "name": "David Brown",
        "email": "<EMAIL>",
        "phone": "******-123-4571",
        "admin_id": "ADMIN-001",
        "permissions": ["all"]
    }
}

def hash_password(password: str) -> str:
    """Hash password for security (simplified for demo)."""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_credentials(username: str, password: str) -> Optional[Dict]:
    """Verify user credentials and return user info if valid."""
    if username in USERS_DB:
        user = USERS_DB[username]
        # In production, compare hashed passwords
        if user["password"] == password:
            return user
    return None

def apply_login_css():
    """Apply CSS styling for login page."""
    st.markdown("""
    <style>
    .login-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 2rem;
        background-color: var(--card-bg, #ffffff);
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--border-color, #dee2e6);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 2rem;
        color: var(--primary, #4e73df);
    }
    
    .login-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .role-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 2px;
    }
    
    .role-user { background-color: #e3f2fd; color: #1976d2; }
    .role-technician { background-color: #fff3e0; color: #f57c00; }
    .role-admin { background-color: #fce4ec; color: #c2185b; }
    
    .demo-accounts {
        background-color: var(--accent, #f8f9fa);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid var(--border-color, #dee2e6);
    }
    
    .demo-accounts h4 {
        margin-top: 0;
        color: var(--text-main, #212529);
    }
    
    .demo-account {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--border-color, #dee2e6);
    }
    
    .demo-account:last-child {
        border-bottom: none;
    }
    
    .account-info {
        flex-grow: 1;
    }
    
    .account-username {
        font-weight: bold;
        color: var(--text-main, #212529);
    }
    
    .account-name {
        font-size: 0.9rem;
        color: var(--text-secondary, #6c757d);
    }
    </style>
    """, unsafe_allow_html=True)

def show_demo_accounts():
    """Show demo accounts for easy testing."""
    st.markdown("""
    <div class="demo-accounts">
        <h4>🔑 Demo Accounts</h4>
        <div class="demo-account">
            <div class="account-info">
                <div class="account-username">user1</div>
                <div class="account-name">John Doe</div>
            </div>
            <span class="role-badge role-user">User</span>
        </div>
        <div class="demo-account">
            <div class="account-info">
                <div class="account-username">tech1</div>
                <div class="account-name">Mike Johnson</div>
            </div>
            <span class="role-badge role-technician">Technician</span>
        </div>
        <div class="demo-account">
            <div class="account-info">
                <div class="account-username">admin1</div>
                <div class="account-name">David Brown</div>
            </div>
            <span class="role-badge role-admin">Admin</span>
        </div>
        <p style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 1rem;">
            Password for all accounts: password123 (users), tech123 (technicians), admin123 (admin)
        </p>
    </div>
    """, unsafe_allow_html=True)

def login_form():
    """Display login form."""
    st.markdown("""
    <div class="login-container">
        <div class="login-header">
            <h1>🎫 TeamLogic-AutoTask</h1>
            <p>Please sign in to continue</p>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    with st.form("login_form"):
        username = st.text_input("Username", placeholder="Enter your username")
        password = st.text_input("Password", type="password", placeholder="Enter your password")
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            login_button = st.form_submit_button("🔐 Sign In", use_container_width=True)
        
        if login_button:
            if username and password:
                user_info = verify_credentials(username, password)
                if user_info:
                    # Store user session
                    st.session_state.authenticated = True
                    st.session_state.username = username
                    st.session_state.user_role = user_info["role"]
                    st.session_state.user_info = user_info
                    
                    # Set role-specific session data
                    if user_info["role"] == "user":
                        st.session_state.current_user = username
                    elif user_info["role"] == "technician":
                        st.session_state.tech_name = user_info["name"]
                        st.session_state.tech_id = user_info.get("tech_id", "TECH-001")
                    elif user_info["role"] == "admin":
                        st.session_state.admin_name = user_info["name"]
                        st.session_state.admin_id = user_info.get("admin_id", "ADMIN-001")
                    
                    st.success(f"✅ Welcome, {user_info['name']}!")
                    st.rerun()
                else:
                    st.error("❌ Invalid username or password")
            else:
                st.warning("⚠️ Please enter both username and password")
    
    # Show demo accounts
    show_demo_accounts()

def logout():
    """Handle user logout."""
    # Clear all session state
    for key in list(st.session_state.keys()):
        del st.session_state[key]
    st.success("👋 You have been logged out successfully!")
    st.rerun()

def check_authentication():
    """Check if user is authenticated and return role."""
    if st.session_state.get("authenticated", False):
        return st.session_state.get("user_role", None)
    return None

def create_logout_button():
    """Create logout button in sidebar."""
    with st.sidebar:
        st.markdown("---")
        if st.button("🚪 Logout", key="logout_btn", use_container_width=True):
            logout()

def main():
    """Main login application."""
    st.set_page_config(
        page_title="TeamLogic-AutoTask - Login",
        layout="centered",
        page_icon="🎫",
        initial_sidebar_state="collapsed"
    )
    
    # Apply CSS
    apply_login_css()
    
    # Check authentication
    user_role = check_authentication()
    
    if not user_role:
        # Show login form
        login_form()
    else:
        # User is authenticated, redirect to appropriate dashboard
        st.success(f"✅ Authenticated as {st.session_state.user_info['name']} ({user_role})")
        st.info("🔄 Redirecting to your dashboard...")
        
        # This would be handled by the main app.py routing
        if user_role == "user":
            st.markdown("**Redirecting to User Dashboard...**")
        elif user_role == "technician":
            st.markdown("**Redirecting to Technician Dashboard...**")
        elif user_role == "admin":
            st.markdown("**Redirecting to Admin Dashboard...**")

if __name__ == "__main__":
    main()

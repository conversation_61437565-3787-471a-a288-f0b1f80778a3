"""
Database service for TeamLogic-AutoTask application.
Handles connections to Snowflake and database operations for the real tables.
"""

import streamlit as st
import pandas as pd
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import os
import sys

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database.snowflake_db import SnowflakeConnection
from src.agents.intake_agent import IntakeClassificationAgent

class DatabaseService:
    """Service class for database operations with Snowflake."""
    
    def __init__(self):
        """Initialize the database service."""
        self.db_connection = None
        self.intake_agent = None
        self._initialize_connection()
    
    def _initialize_connection(self):
        """Initialize Snowflake connection using environment variables or Streamlit secrets."""
        try:
            # Try to get credentials from Streamlit secrets first
            if hasattr(st, 'secrets') and 'snowflake' in st.secrets:
                credentials = st.secrets['snowflake']
            else:
                # Fallback to environment variables
                credentials = {
                    'account': os.getenv('SNOWFLAKE_ACCOUNT'),
                    'user': os.getenv('SNOWFLAKE_USER'),
                    'password': os.getenv('SNOWFLAKE_PASSWORD'),
                    'warehouse': os.getenv('SNOWFLAKE_WAREHOUSE'),
                    'database': os.getenv('SNOWFLAKE_DATABASE'),
                    'schema': os.getenv('SNOWFLAKE_SCHEMA'),
                    'role': os.getenv('SNOWFLAKE_ROLE'),
                    'passcode': os.getenv('SNOWFLAKE_PASSCODE', '')
                }
            
            # Initialize database connection
            self.db_connection = SnowflakeConnection(
                sf_account=credentials['account'],
                sf_user=credentials['user'],
                sf_password=credentials['password'],
                sf_warehouse=credentials['warehouse'],
                sf_database=credentials['database'],
                sf_schema=credentials['schema'],
                sf_role=credentials['role'],
                sf_passcode=credentials['passcode']
            )
            
            # Initialize intake agent for workflow
            self.intake_agent = IntakeClassificationAgent(
                sf_account=credentials['account'],
                sf_user=credentials['user'],
                sf_password=credentials['password'],
                sf_warehouse=credentials['warehouse'],
                sf_database=credentials['database'],
                sf_schema=credentials['schema'],
                sf_role=credentials['role'],
                sf_passcode=credentials['passcode']
            )
            
            print("✅ Database service initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing database service: {e}")
            self.db_connection = None
            self.intake_agent = None
    
    def get_users(self) -> List[Dict]:
        """Get all users from user_dummy_data table."""
        if not self.db_connection:
            return []
        
        query = """
        SELECT name, useremail, userid, userphonenumber
        FROM user_dummy_data
        ORDER BY name
        """
        
        try:
            results = self.db_connection.execute_query(query)
            return results if results else []
        except Exception as e:
            print(f"Error fetching users: {e}")
            return []
    
    def get_technicians(self) -> List[Dict]:
        """Get all technicians from technician_dummy_data table."""
        if not self.db_connection:
            return []
        
        query = """
        SELECT technician_id, name, email, roll, skill, current_workload, specialization
        FROM technician_dummy_data
        ORDER BY current_workload ASC, name
        """
        
        try:
            results = self.db_connection.execute_query(query)
            return results if results else []
        except Exception as e:
            print(f"Error fetching technicians: {e}")
            return []
    
    def get_tickets(self, limit: int = 100) -> List[Dict]:
        """Get tickets from tickets table."""
        if not self.db_connection:
            return []
        
        query = f"""
        SELECT *
        FROM tickets
        ORDER BY created_at DESC
        LIMIT {limit}
        """
        
        try:
            results = self.db_connection.execute_query(query)
            return results if results else []
        except Exception as e:
            print(f"Error fetching tickets: {e}")
            return []
    
    def get_user_tickets(self, user_email: str) -> List[Dict]:
        """Get tickets for a specific user."""
        if not self.db_connection:
            return []
        
        query = """
        SELECT *
        FROM tickets
        WHERE requester_email = %s
        ORDER BY created_at DESC
        """
        
        try:
            results = self.db_connection.execute_query(query, (user_email,))
            return results if results else []
        except Exception as e:
            print(f"Error fetching user tickets: {e}")
            return []
    
    def get_technician_tickets(self, technician_id: str) -> List[Dict]:
        """Get tickets assigned to a specific technician."""
        if not self.db_connection:
            return []
        
        query = """
        SELECT *
        FROM tickets
        WHERE assigned_technician = %s
        ORDER BY priority DESC, created_at ASC
        """
        
        try:
            results = self.db_connection.execute_query(query, (technician_id,))
            return results if results else []
        except Exception as e:
            print(f"Error fetching technician tickets: {e}")
            return []
    
    def create_ticket(self, ticket_data: Dict) -> str:
        """
        Create a new ticket and trigger the workflow.
        
        Args:
            ticket_data (Dict): Ticket information
            
        Returns:
            str: Generated ticket ID
        """
        if not self.db_connection:
            raise Exception("Database connection not available")
        
        # Generate unique ticket ID
        ticket_id = f"TKT-{uuid.uuid4().hex[:8].upper()}"
        
        # Prepare ticket data for database
        current_time = datetime.now().isoformat()
        
        # Insert ticket into database
        insert_query = """
        INSERT INTO tickets (
            ticket_id, title, description, priority, status,
            requester_name, requester_email, requester_phone,
            company_id, device_model, os_version, error_message,
            location, category, created_at, updated_at
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        
        try:
            self.db_connection.execute_query(insert_query, (
                ticket_id,
                ticket_data.get('title', ''),
                ticket_data.get('description', ''),
                ticket_data.get('priority', 'Medium'),
                'Open',  # Initial status
                ticket_data.get('requester_name', ''),
                ticket_data.get('email', ''),
                ticket_data.get('phone', ''),
                ticket_data.get('company_id', ''),
                ticket_data.get('device_model', ''),
                ticket_data.get('os_version', ''),
                ticket_data.get('error_message', ''),
                ticket_data.get('location', ''),
                ticket_data.get('category', 'General'),
                current_time,
                current_time
            ))
            
            # Trigger the workflow for classification and assignment
            if self.intake_agent:
                try:
                    workflow_result = self.intake_agent.process_ticket(
                        title=ticket_data.get('title', ''),
                        description=ticket_data.get('description', ''),
                        priority=ticket_data.get('priority', 'Medium'),
                        requester_email=ticket_data.get('email', ''),
                        ticket_id=ticket_id
                    )
                    
                    # Update ticket with classification results
                    if workflow_result:
                        self._update_ticket_from_workflow(ticket_id, workflow_result)
                    
                except Exception as e:
                    print(f"Warning: Workflow processing failed: {e}")
                    # Continue even if workflow fails
            
            return ticket_id
            
        except Exception as e:
            print(f"Error creating ticket: {e}")
            raise Exception(f"Failed to create ticket: {e}")
    
    def _update_ticket_from_workflow(self, ticket_id: str, workflow_result: Dict):
        """Update ticket with results from workflow processing."""
        try:
            update_query = """
            UPDATE tickets 
            SET 
                assigned_technician = %s,
                status = %s,
                category = %s,
                subcategory = %s,
                resolution_notes = %s,
                updated_at = %s
            WHERE ticket_id = %s
            """
            
            current_time = datetime.now().isoformat()
            
            self.db_connection.execute_query(update_query, (
                workflow_result.get('assigned_technician', ''),
                workflow_result.get('status', 'Assigned'),
                workflow_result.get('category', ''),
                workflow_result.get('subcategory', ''),
                workflow_result.get('resolution', ''),
                current_time,
                ticket_id
            ))
            
        except Exception as e:
            print(f"Error updating ticket from workflow: {e}")
    
    def update_ticket_status(self, ticket_id: str, status: str, technician_id: str = None) -> bool:
        """Update ticket status."""
        if not self.db_connection:
            return False
        
        query = """
        UPDATE tickets 
        SET status = %s, updated_at = %s
        WHERE ticket_id = %s
        """
        
        try:
            current_time = datetime.now().isoformat()
            self.db_connection.execute_query(query, (status, current_time, ticket_id))
            return True
        except Exception as e:
            print(f"Error updating ticket status: {e}")
            return False
    
    def update_ticket_priority(self, ticket_id: str, priority: str) -> bool:
        """Update ticket priority."""
        if not self.db_connection:
            return False
        
        query = """
        UPDATE tickets 
        SET priority = %s, updated_at = %s
        WHERE ticket_id = %s
        """
        
        try:
            current_time = datetime.now().isoformat()
            self.db_connection.execute_query(query, (priority, current_time, ticket_id))
            return True
        except Exception as e:
            print(f"Error updating ticket priority: {e}")
            return False
    
    def add_work_note(self, ticket_id: str, note: str, technician_id: str) -> bool:
        """Add a work note to a ticket."""
        if not self.db_connection:
            return False
        
        # For now, append to resolution_notes field
        # In a real system, you might have a separate work_notes table
        query = """
        UPDATE tickets 
        SET resolution_notes = COALESCE(resolution_notes, '') || %s,
            updated_at = %s
        WHERE ticket_id = %s
        """
        
        try:
            current_time = datetime.now().isoformat()
            note_with_timestamp = f"\n[{current_time}] {technician_id}: {note}"
            self.db_connection.execute_query(query, (note_with_timestamp, current_time, ticket_id))
            return True
        except Exception as e:
            print(f"Error adding work note: {e}")
            return False

# Global database service instance
@st.cache_resource
def get_database_service():
    """Get or create the database service instance."""
    return DatabaseService()

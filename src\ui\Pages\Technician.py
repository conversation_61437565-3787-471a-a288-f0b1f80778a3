import streamlit as st
import json
import os
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta, date
from collections import Counter
from typing import List, Dict
import random

# Import shared UI components
from src.ui.components import (
    apply_custom_css, create_metric_card, create_status_badge,
    create_header_section, create_footer_section, format_time_elapsed,
    format_date_display, create_priority_icon, create_data_table,
    create_chart_container, create_filter_section, generate_unique_key
)

# Import database service
from src.services.database_service import get_database_service

# Configuration Constants
PAGE_TITLE = "TechLogic - Technician Dashboard"
LAYOUT = "wide"
PAGE_ICON = "🔧"
PRIORITY_OPTIONS = ["Low", "Medium", "High", "Critical", "Desktop/User Down"]
STATUS_OPTIONS = ["Assigned", "In Progress", "Pending Parts", "Resolved", "Closed"]
TECHNICIAN_STATUS = ["Available", "Busy", "On Break", "Off Duty"]
TICKET_TYPES = ["Hardware", "Software", "Network", "Security", "General"]
SUPPORT_PHONE = "9723100860"
SUPPORT_EMAIL = "<EMAIL>"

# Initialize database service
@st.cache_resource
def get_db_service():
    """Get database service instance."""
    return get_database_service()

# Priority and Status Colors
PRIORITY_COLORS = {
    "Low": "🟢",
    "Medium": "🟡", 
    "High": "🟠",
    "Critical": "🔴",
    "Desktop/User Down": "🚨"
}

STATUS_COLORS = {
    "Assigned": "#4e73df",
    "In Progress": "#f6c23e", 
    "Pending Parts": "#fd7e14",
    "Resolved": "#36b9cc", 
    "Closed": "#1cc88a"
}

def create_enhanced_technician_header(tech_name: str, tech_id: str, tech_status: str):
    """Create an enhanced header section for technician dashboard."""
    current_time = datetime.now().strftime("%H:%M")
    current_theme = st.session_state.get('theme', 'dark')
    theme_icon = "🌙" if current_theme == 'dark' else "☀️"

    create_header_section(
        title=f"🔧 {PAGE_TITLE}",
        subtitle=f"Welcome back, {tech_name}! Manage and resolve support tickets efficiently."
    )

    # Technician info card
    st.markdown(f"""
    <div class="user-info-section">
        <h4>👨‍🔧 Technician Information</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
            <div>
                <strong>Name:</strong> {tech_name}<br>
                <strong>ID:</strong> {tech_id}<br>
                <strong>Status:</strong> {tech_status}
            </div>
            <div>
                <strong>Current Time:</strong> {current_time}<br>
                <strong>Theme:</strong> {theme_icon} {current_theme.title()}<br>
                <strong>Shift:</strong> Day Shift
            </div>
            <div>
                <strong>Specialization:</strong> Hardware/Software<br>
                <strong>Experience:</strong> 5+ years<br>
                <strong>Certification:</strong> CompTIA A+
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def apply_technician_css():
    """Apply technician-specific CSS styling."""
    st.markdown("""
    <style>
    .tech-card {
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        border: 1px solid var(--border-color);
        border-left: 4px solid var(--success);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .urgent-section {
        background-color: rgba(220, 53, 69, 0.1);
        border: 2px solid var(--danger);
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
    }

    .ticket-action-btn {
        margin: 5px;
        padding: 8px 16px;
        border-radius: 5px;
        border: none;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-start { background-color: var(--success); color: white; }
    .btn-resolve { background-color: var(--primary); color: white; }
    .btn-note { background-color: var(--info); color: white; }
    .btn-contact { background-color: var(--warning); color: black; }

    .performance-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }

    .ticket-priority-critical {
        border-left: 4px solid var(--danger);
        background-color: rgba(220, 53, 69, 0.05);
    }

    .ticket-priority-high {
        border-left: 4px solid var(--warning);
        background-color: rgba(255, 193, 7, 0.05);
    }

    .ticket-priority-medium {
        border-left: 4px solid var(--info);
        background-color: rgba(23, 162, 184, 0.05);
    }

    .ticket-priority-low {
        border-left: 4px solid var(--success);
        background-color: rgba(40, 167, 69, 0.05);
    }
    </style>
    """, unsafe_allow_html=True)

def apply_custom_css():
    """Apply custom CSS styling with theme support for technician dashboard."""
    # Import and use the shared apply_custom_css from components
    from src.ui.components import apply_custom_css as shared_apply_css
    shared_apply_css()
    # Add technician-specific styles
    apply_technician_css()

def old_apply_custom_css():
    """Legacy CSS function - keeping for reference."""
    # Get current theme from session state
    theme = st.session_state.get('theme', 'dark')

    if theme == 'dark':
        # Dark Theme Variables
        theme_vars = """
        :root {
            --primary: #28a745;
            --primary-hover: #218838;
            --secondary: #181818;
            --accent: #23272f;
            --text-main: #f8f9fa;
            --text-secondary: #b0b3b8;
            --card-bg: #23272f;
            --sidebar-bg: #111111;
            --tech-accent: #17a2b8;
            --border-color: #444;
            --hover-bg: #2d2d2d;
            --urgent-bg: #2d1b1b;
            --progress-bg: #2d2a1b;
            --success-bg: #1e5f3a;
            --info-bg: #1e3a5f;
        }
        """
    else:
        # Light Theme Variables
        theme_vars = """
        :root {
            --primary: #007bff;
            --primary-hover: #0056b3;
            --secondary: #ffffff;
            --accent: #f8f9fa;
            --text-main: #212529;
            --text-secondary: #6c757d;
            --card-bg: #ffffff;
            --sidebar-bg: #f8f9fa;
            --tech-accent: #17a2b8;
            --border-color: #dee2e6;
            --hover-bg: #f8f9fa;
            --urgent-bg: #fff5f5;
            --progress-bg: #fffbf0;
            --success-bg: #f0fff4;
            --info-bg: #f0f8ff;
        }
        """
    
    st.markdown(f"""
    <style>
    {theme_vars}
    
    .main {{
        background-color: var(--secondary);
        color: var(--text-main);
    }}
    
    body, .stApp, .main, .block-container {{
        background-color: var(--secondary) !important;
        color: var(--text-main) !important;
    }}
    
    .stTextInput input, .stTextArea textarea,
    .stSelectbox select, .stDateInput input {{
        background-color: var(--card-bg) !important;
        color: var(--text-main) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 6px !important;
    }}
    
    .stButton>button {{
        background-color: var(--primary) !important;
        color: white !important;
        border: none;
        padding: 10px 24px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }}
    
    .stButton>button:hover {{
        background-color: var(--primary-hover) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }}
    
    .tech-card {{
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        margin-bottom: 20px;
        color: var(--text-main);
        border-left: 4px solid var(--tech-accent);
        border: 1px solid var(--border-color);
    }}
    
    .urgent-ticket {{
        border-left: 4px solid #dc3545 !important;
        background-color: var(--urgent-bg) !important;
    }}
    
    .in-progress-ticket {{
        border-left: 4px solid #ffc107 !important;
        background-color: var(--progress-bg) !important;
    }}
    
    .sidebar .sidebar-content, .stSidebar, section[data-testid="stSidebar"] {{
        background-color: var(--sidebar-bg) !important;
        color: var(--text-main) !important;
    }}
    
    .status-badge {{
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8em;
        font-weight: bold;
        text-align: center;
        display: inline-block;
        margin: 2px;
    }}
    
    .status-assigned {{ background-color: #4e73df; color: white; }}
    .status-in-progress {{ background-color: #f6c23e; color: black; }}
    .status-pending {{ background-color: #fd7e14; color: white; }}
    .status-resolved {{ background-color: #36b9cc; color: white; }}
    .status-closed {{ background-color: #1cc88a; color: white; }}
    
    /* Theme Toggle Button Styling */
    .theme-toggle {{
        background-color: var(--accent);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 5px 15px;
        color: var(--text-main);
        font-size: 0.9em;
        margin-bottom: 10px;
    }}
    
    /* Expander Styling */
    .streamlit-expanderHeader {{
        background-color: var(--card-bg) !important;
        color: var(--text-main) !important;
        border: 1px solid var(--border-color) !important;
    }}
    
    .streamlit-expanderContent {{
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
    }}
    
    /* Metric Styling */
    .metric-container {{
        background-color: var(--card-bg);
        padding: 15px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        text-align: center;
    }}
    
    /* Form Styling */
    .stForm {{
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
    }}
    
    /* Selectbox Styling */
    .stSelectbox > div > div {{
        background-color: var(--card-bg) !important;
        color: var(--text-main) !important;
    }}
    
    /* Multiselect Styling */
    .stMultiSelect > div > div {{
        background-color: var(--card-bg) !important;
        color: var(--text-main) !important;
    }}
    
    /* Success/Info/Warning Messages */
    .stSuccess {{
        background-color: var(--success-bg) !important;
        color: var(--text-main) !important;
    }}
    
    .stInfo {{
        background-color: var(--info-bg) !important;
        color: var(--text-main) !important;
    }}
    
    .stWarning {{
        background-color: var(--progress-bg) !important;
        color: var(--text-main) !important;
    }}
    
    /* Chart Background */
    .js-plotly-plot {{
        background-color: var(--card-bg) !important;
    }}
    
    </style>
    """, unsafe_allow_html=True)

def create_theme_toggle():
    """Create theme toggle in sidebar"""
    current_theme = st.session_state.get('theme', 'dark')
    
    st.markdown("### 🎨 Theme")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🌙 Dark", key="dark_theme", use_container_width=True, 
                    type="primary" if current_theme == 'dark' else "secondary"):
            st.session_state.theme = 'dark'
            st.rerun()
    
    with col2:
        if st.button("☀️ Light", key="light_theme", use_container_width=True,
                    type="primary" if current_theme == 'light' else "secondary"):
            st.session_state.theme = 'light'
            st.rerun()
    
    st.markdown(f"**Current:** {current_theme.title()} Mode")

def create_technician_sidebar():
    """Create the technician navigation sidebar."""
    with st.sidebar:
        # Theme Toggle at the top
        create_theme_toggle()
        
        st.markdown("---")
        
        # Technician Profile Section
        st.markdown("## 👨‍🔧 Technician Profile")
        
        # Mock technician info
        tech_name = st.session_state.get('tech_name', 'John Smith')
        tech_id = st.session_state.get('tech_id', 'TECH-001')
        
        st.markdown(f"**Name:** {tech_name}")
        st.markdown(f"**ID:** {tech_id}")
        
        # Status selector
        current_status = st.selectbox(
            "Status:",
            TECHNICIAN_STATUS,
            index=0,
            key="tech_status"
        )
        
        st.markdown("---")
        
        # Navigation
        st.markdown("## 🧭 Navigation")

        if st.button("🏠 My Dashboard", key="nav_dashboard", use_container_width=True):
            st.session_state.page = "dashboard"
            st.rerun()

        if st.button("📋 My Tickets", key="nav_my_tickets", use_container_width=True):
            st.session_state.page = "my_tickets"
            st.rerun()

        if st.button("⚡ Urgent Tickets", key="nav_urgent", use_container_width=True):
            st.session_state.page = "urgent_tickets"
            st.rerun()

        if st.button("📊 Work Analytics", key="nav_analytics", use_container_width=True):
            st.session_state.page = "analytics"
            st.rerun()

        # Show current page
        current_page = st.session_state.get('page', 'dashboard')
        st.markdown(f"**Current:** {current_page.replace('_', ' ').title()}")

        st.markdown("---")

        # Quick Stats for Technician
        st.markdown("### 📈 Today's Stats")
        
        # Mock stats
        assigned_today = random.randint(3, 8)
        completed_today = random.randint(1, 5)
        in_progress = random.randint(1, 3)
        
        st.metric("Assigned Today", assigned_today)
        st.metric("Completed", completed_today)
        st.metric("In Progress", in_progress)
        
        # Performance indicator
        completion_rate = (completed_today / max(assigned_today, 1)) * 100
        st.metric("Completion Rate", f"{completion_rate:.1f}%")

        st.markdown("---")
        
        # Quick Actions
        st.markdown("### ⚡ Quick Actions")
        if st.button("🔄 Refresh Data", key="refresh_data", use_container_width=True):
            st.rerun()
        
        if st.button("📝 Add Work Note", key="add_note", use_container_width=True):
            st.session_state.show_note_modal = True

        # Enhanced Contact Information
        st.markdown("---")
        st.markdown("### 📞 Support & Contact")
        st.markdown(f"**Phone:** {SUPPORT_PHONE}")
        st.markdown(f"**Email:** {SUPPORT_EMAIL}")
        st.markdown("**Emergency:** Ext. 911")
        st.markdown("**Hours:** 24/7 Support")

def create_enhanced_technician_sidebar():
    """Create an enhanced technician navigation sidebar with better functionality."""
    with st.sidebar:
        # Enhanced Theme Toggle
        create_theme_toggle()

        st.markdown("---")

        # Enhanced Technician Profile Section
        st.markdown("### 👨‍🔧 Technician Profile")

        tech_name = st.session_state.get('tech_name', 'John Smith')
        tech_id = st.session_state.get('tech_id', 'TECH-001')

        st.markdown(f"**Name:** {tech_name}")
        st.markdown(f"**ID:** {tech_id}")
        st.markdown(f"**Specialization:** Hardware/Software")

        # Enhanced Status selector with colors
        current_status = st.selectbox(
            "Current Status:",
            TECHNICIAN_STATUS,
            index=0,
            key="enhanced_tech_status",
            help="Update your current availability status"
        )

        # Status indicator with colors
        status_colors = {
            "Available": "🟢",
            "Busy": "🔴",
            "On Break": "🟡",
            "Off Duty": "⚫"
        }
        st.markdown(f"Status: {status_colors.get(current_status, '⚪')} {current_status}")

        st.markdown("---")

        # Enhanced Navigation with better organization
        st.markdown("### 🧭 Navigation")

        nav_buttons = [
            ("🏠 Dashboard", "dashboard"),
            ("📋 My Tickets", "my_tickets"),
            ("⚡ Urgent Tickets", "urgent_tickets"),
            ("📊 Analytics", "analytics")
        ]

        current_page = st.session_state.get('page', 'dashboard')

        for label, page in nav_buttons:
            button_type = "primary" if current_page == page else "secondary"
            if st.button(label, key=f"enhanced_nav_{page}", use_container_width=True, type=button_type):
                st.session_state.page = page
                st.rerun()

        st.markdown(f"**Current Page:** {current_page.replace('_', ' ').title()}")

        st.markdown("---")

        # Enhanced Performance Stats with real data
        st.markdown("### 📈 Performance Dashboard")

        # Generate realistic stats based on mock data
        tech_tickets = generate_technician_tickets()
        assigned_today = len([t for t in tech_tickets if t['status'] == 'Assigned'])
        completed_today = len([t for t in tech_tickets if t['status'] == 'Resolved'])
        in_progress = len([t for t in tech_tickets if t['status'] == 'In Progress'])
        urgent_count = len([t for t in tech_tickets if t['priority'] in ['Critical', 'Desktop/User Down']])

        # Display metrics with better formatting
        col1, col2 = st.columns(2)
        with col1:
            st.metric("📋 Assigned", assigned_today)
            st.metric("⚡ In Progress", in_progress)
        with col2:
            st.metric("✅ Completed", completed_today)
            st.metric("🚨 Urgent", urgent_count)

        # Performance indicators
        if assigned_today > 0:
            completion_rate = (completed_today / assigned_today) * 100
            st.metric("📊 Completion Rate", f"{completion_rate:.1f}%")

            # Performance badge
            if completion_rate >= 80:
                st.success("🏆 Excellent Performance!")
            elif completion_rate >= 60:
                st.info("👍 Good Performance")
            else:
                st.warning("📈 Room for Improvement")

        st.markdown("---")

        # Enhanced Quick Actions
        st.markdown("### ⚡ Quick Actions")

        if st.button("🔄 Refresh Data", key="enhanced_refresh", use_container_width=True):
            st.success("✅ Data refreshed!")
            st.rerun()

        if st.button("📝 Quick Note", key="enhanced_note", use_container_width=True):
            st.info("📝 Quick note feature activated")

        if st.button("📊 View Reports", key="enhanced_reports", use_container_width=True):
            st.info("📊 Opening performance reports...")

        st.markdown("---")

        # Enhanced Support Information
        st.markdown("### 📞 Support & Emergency")
        st.markdown(f"**Main:** {SUPPORT_PHONE}")
        st.markdown(f"**Email:** {SUPPORT_EMAIL}")
        st.markdown("**Emergency:** Ext. 911")
        st.markdown("**Supervisor:** Ext. 100")
        st.markdown("**Hours:** 24/7 Available")

def get_technician_tickets(tech_id: str = None) -> List[Dict]:
    """Get tickets assigned to a specific technician from database"""
    try:
        db_service = get_db_service()

        if tech_id:
            tickets = db_service.get_technician_tickets(tech_id)
        else:
            # Get all tickets if no specific technician
            tickets = db_service.get_tickets(limit=50)

        # Convert database format to UI format
        formatted_tickets = []
        for ticket in tickets:
            formatted_ticket = {
                "id": ticket.get('TICKET_ID', ''),
                "title": ticket.get('TITLE', ''),
                "description": ticket.get('DESCRIPTION', ''),
                "created_at": ticket.get('CREATED_AT', ''),
                "status": ticket.get('STATUS', 'Open'),
                "priority": ticket.get('PRIORITY', 'Medium'),
                "category": ticket.get('CATEGORY', 'General'),
                "requester_name": ticket.get('REQUESTER_NAME', ''),
                "requester_email": ticket.get('REQUESTER_EMAIL', ''),
                "requester_phone": ticket.get('REQUESTER_PHONE', ''),
                "company_id": ticket.get('COMPANY_ID', ''),
                "device_model": ticket.get('DEVICE_MODEL', ''),
                "os_version": ticket.get('OS_VERSION', ''),
                "error_message": ticket.get('ERROR_MESSAGE', ''),
                "location": ticket.get('LOCATION', ''),
                "updated_at": ticket.get('UPDATED_AT', ''),
                "assigned_technician": ticket.get('ASSIGNED_TECHNICIAN', ''),
                "resolution_notes": ticket.get('RESOLUTION_NOTES', ''),
            }
            formatted_tickets.append(formatted_ticket)

        return formatted_tickets

    except Exception as e:
        st.error(f"Error fetching technician tickets: {e}")
        return []

# Removed old dummy data generation function
    
    mock_locations = [
        "Building A - Floor 2", "Building B - Floor 1", "Building A - Floor 3",
        "Building C - Reception", "Building B - Floor 2", "Remote Location",
        "Building A - IT Room", "Building C - IT Room", "Building B - Floor 3"
    ]
    
    mock_requesters = [
        {"name": "Alice Johnson", "email": "<EMAIL>"},
        {"name": "Bob Wilson", "email": "<EMAIL>"},
        {"name": "Carol Davis", "email": "<EMAIL>"},
        {"name": "David Brown", "email": "<EMAIL>"},
        {"name": "Emma Taylor", "email": "<EMAIL>"},
        {"name": "Frank Miller", "email": "<EMAIL>"},
        {"name": "Grace Lee", "email": "<EMAIL>"},
        {"name": "Henry Clark", "email": "<EMAIL>"},
        {"name": "Ivy Martinez", "email": "<EMAIL>"},
        {"name": "Jack Anderson", "email": "<EMAIL>"}
    ]
    
    tickets = []
    for i in range(count):
        created_time = datetime.now() - timedelta(
            hours=random.randint(1, 168),  # Last week
            minutes=random.randint(0, 59)
        )
        
        # Assign status based on time (newer tickets more likely to be assigned/in progress)
        hours_old = (datetime.now() - created_time).total_seconds() / 3600
        if hours_old < 2:
            status = random.choice(["Assigned", "In Progress"])
        elif hours_old < 24:
            status = random.choice(["Assigned", "In Progress", "Pending Parts"])
        else:
            status = random.choice(["In Progress", "Pending Parts", "Resolved", "Closed"])
        
        issue_data = random.choice(mock_issues)
        requester_data = random.choice(mock_requesters)
        
        ticket = {
            "id": f"TKT-{2000 + i}",
            "title": issue_data["title"],
            "description": issue_data["description"],
            "created_at": created_time.isoformat(),
            "assigned_at": (created_time + timedelta(minutes=random.randint(5, 60))).isoformat(),
            "status": status,
            "priority": random.choice(PRIORITY_OPTIONS),
            "category": issue_data["category"],  # Backend classified
            "requester_name": requester_data["name"],
            "requester_email": requester_data["email"],
            "requester_phone": f"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
            "location": random.choice(mock_locations),
            "assigned_technician": tech_id,
            "estimated_hours": random.randint(1, 8),
            "actual_hours": random.randint(1, 6) if status in ["Resolved", "Closed"] else None,
            "parts_needed": random.choice([None, "RAM Module", "Hard Drive", "Network Cable", "Power Supply"]),
            "resolution_steps": issue_data["resolution_steps"],  # Backend generated
            "work_notes": [],
            "updated_at": created_time.isoformat()
        }
        
        # Add work notes for tickets in progress or completed
        if status in ["In Progress", "Resolved", "Closed"]:
            ticket["work_notes"] = [
                {
                    "timestamp": (created_time + timedelta(hours=1)).isoformat(),
                    "note": "Initial diagnosis completed. Issue identified.",
                    "technician": tech_id
                }
            ]
            
        if status in ["Resolved", "Closed"]:
            ticket["work_notes"].append({
                "timestamp": (created_time + timedelta(hours=2)).isoformat(),
                "note": "Solution implemented and tested. Issue resolved.",
                "technician": tech_id
            })
        
        tickets.append(ticket)
    
    return sorted(tickets, key=lambda x: x["created_at"], reverse=True)

def format_time_elapsed(created_at):
    """Calculate and format time elapsed"""
    try:
        if isinstance(created_at, str):
            ticket_time = datetime.fromisoformat(created_at)
        else:
            ticket_time = created_at

        now = datetime.now()
        diff = now - ticket_time

        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        else:
            minutes = max(1, diff.seconds // 60)
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    except:
        return "Unknown"

def get_chart_theme():
    """Get chart theme based on current theme"""
    theme = st.session_state.get('theme', 'dark')
    
    if theme == 'dark':
        return {
            "plot_bgcolor": "#181818",
            "paper_bgcolor": "#23272f",
            "font_color": "#f8f9fa"
        }
    else:
        return {
            "plot_bgcolor": "#ffffff",
            "paper_bgcolor": "#ffffff", 
            "font_color": "#212529"
        }

def dashboard_page():
    """Enhanced main technician dashboard page with improved layout and functionality."""
    # Generate mock data (In production, this would be API calls)
    tech_tickets = generate_technician_tickets()

    # Enhanced header
    tech_name = st.session_state.get('tech_name', 'John Smith')
    tech_id = st.session_state.get('tech_id', 'TECH-001')
    tech_status = st.session_state.get('tech_status', 'Available')

    create_enhanced_technician_header(tech_name, tech_id, tech_status)

    # Enhanced metrics with better visualization
    assigned_tickets = [t for t in tech_tickets if t['status'] == 'Assigned']
    in_progress_tickets = [t for t in tech_tickets if t['status'] == 'In Progress']
    urgent_tickets = [t for t in tech_tickets if t['priority'] in ['Critical', 'Desktop/User Down']]
    today_tickets = [t for t in tech_tickets if datetime.fromisoformat(t['created_at']).date() == datetime.now().date()]
    resolved_today = [t for t in today_tickets if t['status'] == 'Resolved']

    # Performance metrics section
    st.markdown("### 📊 Performance Metrics")
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        create_metric_card("🎯 Assigned", str(len(assigned_tickets)))
    with col2:
        create_metric_card("⚡ In Progress", str(len(in_progress_tickets)))
    with col3:
        create_metric_card("🚨 Urgent", str(len(urgent_tickets)))
    with col4:
        create_metric_card("📅 Today's Tickets", str(len(today_tickets)))
    with col5:
        completion_rate = (len(resolved_today) / max(len(today_tickets), 1)) * 100
        create_metric_card("✅ Completion Rate", f"{completion_rate:.1f}%")
    
    # Enhanced ticket sections with better organization
    st.markdown("---")

    # 1. Urgent Tickets Section (Always visible if any exist)
    if urgent_tickets:
        st.markdown("""
        <div class="urgent-section">
            <h3>🚨 URGENT TICKETS - IMMEDIATE ATTENTION REQUIRED</h3>
            <p>These tickets require immediate action due to their critical priority level.</p>
        </div>
        """, unsafe_allow_html=True)

        for ticket in urgent_tickets[:3]:  # Show top 3 urgent
            display_enhanced_ticket_card(ticket, is_urgent=True, section="urgent")

        if len(urgent_tickets) > 3:
            with st.expander(f"View {len(urgent_tickets) - 3} more urgent tickets"):
                for ticket in urgent_tickets[3:]:
                    display_enhanced_ticket_card(ticket, is_urgent=True, section="urgent")

    # 2. Assigned Tickets Section
    if assigned_tickets:
        st.markdown("### 📋 Assigned Tickets")
        with st.expander(f"View {len(assigned_tickets)} assigned tickets", expanded=True):
            for ticket in assigned_tickets[:5]:  # Show top 5 assigned
                display_enhanced_ticket_card(ticket, section="assigned")

    # 3. In Progress Tickets Section
    if in_progress_tickets:
        st.markdown("### ⚡ In Progress Tickets")
        with st.expander(f"View {len(in_progress_tickets)} in progress tickets", expanded=True):
            for ticket in in_progress_tickets:
                display_enhanced_ticket_card(ticket, is_in_progress=True, section="in_progress")

def display_enhanced_ticket_card(ticket, is_urgent=False, is_in_progress=False, section="default"):
    """Display enhanced ticket card with all required details"""
    
    # Determine card styling
    card_class = "tech-card"
    if is_urgent:
        card_class += " urgent-ticket"
    elif is_in_progress:
        card_class += " in-progress-ticket"
    
    priority_icon = PRIORITY_COLORS.get(ticket['priority'], '⚪')
    
    # Create expandable ticket card
    with st.expander(f"{priority_icon} {ticket['id']} - {ticket['title']}", expanded=False):
        
        # Ticket Header Information
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.markdown("### 👤 Requester Information")
            st.markdown(f"**Name:** {ticket['requester_name']}")
            st.markdown(f"**Email:** {ticket['requester_email']}")
            st.markdown(f"**Phone:** {ticket['requester_phone']}")
            st.markdown(f"**Location:** {ticket['location']}")
        
        with col2:
            st.markdown("### 🏷️ Ticket Details")
            st.markdown(f"**Category:** {ticket['category']}")
            st.markdown(f"**Priority:** {priority_icon} {ticket['priority']}")
            st.markdown(f"**Status:** {ticket['status']}")
            st.markdown(f"**Estimated Time:** {ticket['estimated_hours']}h")
        
        with col3:
            st.markdown("### ⏰ Timeline")
            st.markdown(f"**Created:** {format_time_elapsed(ticket['created_at'])}")
            st.markdown(f"**Assigned:** {format_time_elapsed(ticket['assigned_at'])}")
            if ticket['actual_hours']:
                st.markdown(f"**Actual Time:** {ticket['actual_hours']}h")
            if ticket['parts_needed']:
                st.markdown(f"**Parts Needed:** {ticket['parts_needed']}")
        
        st.markdown("---")
        
        # Issue Details Section
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.markdown("### 📝 Issue Details")
            st.markdown(f"**Title:** {ticket['title']}")
            st.markdown("**Description:**")
            st.markdown(f"*{ticket['description']}*")
        
        with col2:
            st.markdown("### 🔧 Resolution Steps")
            st.markdown("*Generated by AI Assistant:*")
            for i, step in enumerate(ticket['resolution_steps'], 1):
                st.markdown(f"{i}. {step}")
        
        st.markdown("---")
        
        # Work Notes Section (if any)
        if ticket['work_notes']:
            st.markdown("### 📋 Work Notes")
            for note in ticket['work_notes']:
                note_time = datetime.fromisoformat(note['timestamp']).strftime("%Y-%m-%d %H:%M")
                st.markdown(f"• **{note_time}**: {note['note']}")
            st.markdown("---")
        
        # Enhanced Action Buttons Section
        st.markdown("### ⚡ Quick Actions")

        # Create action buttons based on ticket status
        action_col1, action_col2, action_col3, action_col4 = st.columns(4)

        with action_col1:
            if ticket['status'] == 'Assigned':
                if st.button("▶️ Start Work", key=f"{section}_start_{ticket['id']}",
                           use_container_width=True, type="primary"):
                    st.success(f"✅ Started work on ticket {ticket['id']}")
                    st.balloons()
            elif ticket['status'] == 'In Progress':
                if st.button("✅ Mark Resolved", key=f"{section}_resolve_{ticket['id']}",
                           use_container_width=True, type="primary"):
                    st.success(f"✅ Ticket {ticket['id']} marked as resolved!")
                    st.balloons()

        with action_col2:
            if st.button("📝 Add Note", key=f"{section}_note_{ticket['id']}", use_container_width=True):
                # Show note input modal
                with st.expander("Add Work Note", expanded=True):
                    note_text = st.text_area("Work Note", key=f"{section}_note_text_{ticket['id']}")
                    if st.button("Save Note", key=f"{section}_save_note_{ticket['id']}"):
                        if note_text:
                            st.success("✅ Work note added successfully!")
                        else:
                            st.warning("⚠️ Please enter a note before saving.")

        with action_col3:
            if st.button("📞 Call User", key=f"{section}_call_{ticket['id']}", use_container_width=True):
                st.info(f"📞 Calling {ticket['requester_name']} at {ticket['requester_phone']}")

        with action_col4:
            if st.button("✉️ Email User", key=f"{section}_email_{ticket['id']}", use_container_width=True):
                st.info(f"✉️ Opening email to {ticket['requester_email']}")

        # Priority and Status Update Section
        st.markdown("### 🔧 Update Ticket")
        update_col1, update_col2, update_col3 = st.columns(3)

        with update_col1:
            new_priority = st.selectbox(
                "Priority",
                options=PRIORITY_OPTIONS,
                index=PRIORITY_OPTIONS.index(ticket['priority']),
                key=f"{section}_priority_{ticket['id']}"
            )

        with update_col2:
            new_status = st.selectbox(
                "Status",
                options=STATUS_OPTIONS,
                index=STATUS_OPTIONS.index(ticket['status']),
                key=f"{section}_status_{ticket['id']}"
            )

        with update_col3:
            if st.button("💾 Update", key=f"{section}_update_{ticket['id']}", use_container_width=True):
                if new_priority != ticket['priority'] or new_status != ticket['status']:
                    st.success(f"✅ Ticket {ticket['id']} updated successfully!")
                else:
                    st.info("ℹ️ No changes detected.")
                    st.success("Status updated to 'In Progress'")
                    # In production: API call to update ticket status
            elif ticket['status'] == 'In Progress':
                if st.button("✅ Mark Resolved", key=f"{section}_resolve_{ticket['id']}", use_container_width=True):
                    st.success("Status updated to 'Resolved'")
                    # In production: API call to update ticket status
        
        with col2:
            if st.button("📝 Add Note", key=f"{section}_note_{ticket['id']}", use_container_width=True):
                st.session_state[f"show_note_{section}_{ticket['id']}"] = True
        
        with col3:
            if st.button("📞 Call User", key=f"{section}_call_{ticket['id']}", use_container_width=True):
                st.info(f"Calling {ticket['requester_phone']}...")
                # In production: Integrate with phone system
        
        with col4:
            if st.button("✉️ Email User", key=f"{section}_email_{ticket['id']}", use_container_width=True):
                st.info(f"Opening email to {ticket['requester_email']}...")
                # In production: Open email client or send via API
        
        with col5:
            # Priority change dropdown
            new_priority = st.selectbox(
                "Change Priority:",
                PRIORITY_OPTIONS,
                index=PRIORITY_OPTIONS.index(ticket['priority']),
                key=f"{section}_priority_update_{ticket['id']}"
            )
            if new_priority != ticket['priority']:
                if st.button("Update", key=f"{section}_update_priority_{ticket['id']}", use_container_width=True):
                    st.success(f"Priority updated to {new_priority}")
                    # In production: API call to update priority
        
        # Add note modal (simplified) - UPDATE SESSION STATE KEY
        if st.session_state.get(f"show_note_{section}_{ticket['id']}", False):
            st.markdown("---")
            st.markdown("### 📝 Add Work Note")
            with st.form(f"{section}_note_form_{ticket['id']}"):
                note_text = st.text_area("Work Note:", height=100, placeholder="Enter your work note here...")
                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("💾 Save Note", use_container_width=True):
                        if note_text.strip():
                            st.success("Work note added successfully!")
                            st.session_state[f"show_note_{section}_{ticket['id']}"] = False
                            # In production: API call to save note
                        else:
                            st.error("Please enter a note")
                with col2:
                    if st.form_submit_button("❌ Cancel", use_container_width=True):
                        st.session_state[f"show_note_{section}_{ticket['id']}"] = False

# Backend Integration Helper Functions (Ready for API integration)
def update_ticket_status(ticket_id, new_status, technician_id):
    """Update ticket status - Ready for backend API integration"""
    # In production, this would make an API call
    # Example: requests.put(f"/api/tickets/{ticket_id}/status", {"status": new_status, "technician": technician_id})
    pass

def update_ticket_priority(ticket_id, new_priority, technician_id):
    """Update ticket priority - Ready for backend API integration"""
    # In production, this would make an API call
    # Example: requests.put(f"/api/tickets/{ticket_id}/priority", {"priority": new_priority, "technician": technician_id})
    pass

def add_work_note(ticket_id, note_text, technician_id):
    """Add work note to ticket - Ready for backend API integration"""
    # In production, this would make an API call
    # Example: requests.post(f"/api/tickets/{ticket_id}/notes", {"note": note_text, "technician": technician_id})
    pass

def get_technician_tickets(technician_id):
    """Fetch tickets from backend - Ready for API integration"""
    # In production, this would make an API call
    # Example: response = requests.get(f"/api/technicians/{technician_id}/tickets")
    # return response.json()
    return generate_technician_tickets(technician_id)  # Mock data for now

def my_tickets_page():
    """Page showing all tickets assigned to technician"""
    st.title("📋 My Assigned Tickets")
    
    tech_tickets = generate_technician_tickets()
    
    # Filters
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        status_filter = st.multiselect("Status", STATUS_OPTIONS, default=STATUS_OPTIONS)
    with col2:
        priority_filter = st.multiselect("Priority", PRIORITY_OPTIONS, default=PRIORITY_OPTIONS)
    with col3:
        category_filter = st.multiselect("Category", TICKET_TYPES, default=TICKET_TYPES)
    with col4:
        sort_by = st.selectbox("Sort by", ["Created Date", "Priority", "Status", "Location"])
    
    # Filter tickets
    filtered_tickets = [
        t for t in tech_tickets 
        if t['status'] in status_filter 
        and t['priority'] in priority_filter 
        and t['category'] in category_filter
    ]
    
    # Sort tickets
    if sort_by == "Priority":
        priority_order = {"Critical": 0, "Desktop/User Down": 1, "High": 2, "Medium": 3, "Low": 4}
        filtered_tickets.sort(key=lambda x: priority_order.get(x['priority'], 5))
    elif sort_by == "Status":
        filtered_tickets.sort(key=lambda x: x['status'])
    elif sort_by == "Location":
        filtered_tickets.sort(key=lambda x: x['location'])
    else:  # Created Date
        filtered_tickets.sort(key=lambda x: x['created_at'], reverse=True)
    
    st.markdown(f"**Showing {len(filtered_tickets)} tickets**")
    
    # Display tickets
    for ticket in filtered_tickets:
        is_urgent = ticket['priority'] in ['Critical', 'Desktop/User Down']
        card_class = "urgent-ticket" if is_urgent else "tech-card"
        
        with st.expander(f"{PRIORITY_COLORS[ticket['priority']]} {ticket['id']} - {ticket['title']}", expanded=False):
            # Ticket header
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.markdown(f"**📍 Location:** {ticket['location']}")
                st.markdown(f"**👤 Requester:** {ticket['requester_name']}")
                st.markdown(f"**📞 Phone:** {ticket['requester_phone']}")
                st.markdown(f"**✉️ Email:** {ticket['requester_email']}")
            with col2:
                st.markdown(f"**🏷️ Category:** {ticket['category']}")
                st.markdown(f"**⚡ Priority:** {ticket['priority']}")
                st.markdown(f"**📊 Status:** {ticket['status']}")
                st.markdown(f"**⏱️ Estimated:** {ticket['estimated_hours']}h")
            with col3:
                st.markdown(f"**📅 Created:** {format_time_elapsed(ticket['created_at'])}")
                st.markdown(f"**🔄 Assigned:** {format_time_elapsed(ticket['assigned_at'])}")
                if ticket['actual_hours']:
                    st.markdown(f"**⏰ Actual:** {ticket['actual_hours']}h")
                if ticket['parts_needed']:
                    st.markdown(f"**🔧 Parts:** {ticket['parts_needed']}")
            
            # Description
            st.markdown("**📝 Description:**")
            st.markdown(ticket['description'])
            
            # Work notes
            if ticket['work_notes']:
                st.markdown("**📋 Work Notes:**")
                for note in ticket['work_notes']:
                    note_time = datetime.fromisoformat(note['timestamp']).strftime("%Y-%m-%d %H:%M")
                    st.markdown(f"• *{note_time}*: {note['note']}")
            
            # Action buttons
            st.markdown("---")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                if ticket['status'] == 'Assigned':
                    if st.button("▶️ Start Work", key=f"start_{ticket['id']}"):
                        st.success("Status updated to 'In Progress'")
                elif ticket['status'] == 'In Progress':
                    if st.button("✅ Mark Resolved", key=f"resolve_{ticket['id']}"):
                        st.success("Status updated to 'Resolved'")
            
            with col2:
                if st.button("📝 Add Note", key=f"note_{ticket['id']}"):
                    st.session_state[f"show_note_{ticket['id']}"] = True
            
            with col3:
                if st.button("📞 Call Customer", key=f"call_{ticket['id']}"):
                    st.info(f"Calling {ticket['requester_phone']}...")
            
            with col4:
                new_status = st.selectbox(
                    "Update Status:",
                    STATUS_OPTIONS,
                    index=STATUS_OPTIONS.index(ticket['status']),
                    key=f"status_update_{ticket['id']}"
                )
            
            # Add note modal (simplified)
            if st.session_state.get(f"show_note_{ticket['id']}", False):
                with st.form(f"note_form_{ticket['id']}"):
                    note_text = st.text_area("Work Note:", height=100)
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("Add Note"):
                            st.success("Work note added successfully!")
                            st.session_state[f"show_note_{ticket['id']}"] = False
                    with col2:
                        if st.form_submit_button("Cancel"):
                            st.session_state[f"show_note_{ticket['id']}"] = False

def urgent_tickets_page():
    """Page showing only urgent/critical tickets"""
    st.title("🚨 Urgent Tickets")
    
    tech_tickets = generate_technician_tickets()
    urgent_tickets = [t for t in tech_tickets if t['priority'] in ['Critical', 'Desktop/User Down']]
    
    if not urgent_tickets:
        st.success("🎉 No urgent tickets at the moment!")
        return
    
    st.warning(f"⚠️ {len(urgent_tickets)} urgent tickets require immediate attention!")
    
    # Sort by creation time (newest first)
    urgent_tickets.sort(key=lambda x: x['created_at'], reverse=True)
    
    for i, ticket in enumerate(urgent_tickets):
        with st.container():
            st.markdown(f"""
            <div class="tech-card urgent-ticket">
            <h4>🔥 URGENT #{i+1}: {ticket['id']} - {ticket['title']}</h4>
            </div>
            """, unsafe_allow_html=True)
            
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                st.markdown(f"**📍 Location:** {ticket['location']}")
                st.markdown(f"**👤 Requester:** {ticket['requester_name']}")
                st.markdown(f"**📞 Phone:** {ticket['requester_phone']}")
                st.markdown(f"**📝 Issue:** {ticket['description']}")
            
            with col2:
                st.markdown(f"**⚡ Priority:** {PRIORITY_COLORS[ticket['priority']]} {ticket['priority']}")
                st.markdown(f"**📊 Status:** {ticket['status']}")
                st.markdown(f"**🏷️ Category:** {ticket['category']}")
                st.markdown(f"**⏱️ Estimated:** {ticket['estimated_hours']}h")
            
            with col3:
                st.markdown(f"**📅 Created:** {format_time_elapsed(ticket['created_at'])}")
                st.markdown(f"**🔄 Assigned:** {format_time_elapsed(ticket['assigned_at'])}")
                
                if ticket['status'] == 'Assigned':
                    if st.button(f"🚀 Start Immediately", key=f"urgent_start_{ticket['id']}"):
                        st.success("Started working on urgent ticket!")
                elif ticket['status'] == 'In Progress':
                    if st.button(f"✅ Mark Resolved", key=f"urgent_resolve_{ticket['id']}"):
                        st.success("Urgent ticket resolved!")
            
            st.markdown("---")

def analytics_page():
    """Work analytics and performance page"""
    st.title("📊 Work Analytics & Performance")
    
    tech_tickets = generate_technician_tickets(count=50)
    
    # Time period selector
    col1, col2 = st.columns([1, 3])
    with col1:
        time_period = st.selectbox("Time Period", ["Last 7 days", "Last 30 days", "Last 3 months"])
    
    # Calculate analytics
    total_tickets = len(tech_tickets)
    resolved_tickets = len([t for t in tech_tickets if t['status'] == 'Resolved'])
    avg_resolution_time = sum([t['actual_hours'] for t in tech_tickets if t['actual_hours']]) / max(resolved_tickets, 1)
    
    # Performance metrics
    col1, col2, col3, col4 = st.columns(4)
    col1.metric("Total Tickets", total_tickets)
    col2.metric("Resolved", resolved_tickets)
    col3.metric("Resolution Rate", f"{(resolved_tickets/total_tickets)*100:.1f}%")
    col4.metric("Avg Resolution Time", f"{avg_resolution_time:.1f}h")
    
    # Charts
    st.subheader("📈 Performance Charts")
    
    # Get theme for charts
    chart_theme = get_chart_theme()
    
    # Tickets by status
    status_counts = Counter(t['status'] for t in tech_tickets)
    col1, col2 = st.columns(2)
    
    with col1:
        fig_status = px.pie(
            values=list(status_counts.values()),
            names=list(status_counts.keys()),
            title="Tickets by Status"
        )
        fig_status.update_layout(**chart_theme)
        st.plotly_chart(fig_status, use_container_width=True)
    
    with col2:
        # Tickets by priority
        priority_counts = Counter(t['priority'] for t in tech_tickets)
        fig_priority = px.bar(
            x=list(priority_counts.keys()),
            y=list(priority_counts.values()),
            title="Tickets by Priority"
        )
        fig_priority.update_layout(**chart_theme)
        st.plotly_chart(fig_priority, use_container_width=True)
    
    # Performance trends (mock data)
    st.subheader("📈 Performance Trends")
    dates = [datetime.now().date() - timedelta(days=i) for i in range(7, 0, -1)]
    tickets_per_day = [random.randint(2, 8) for _ in dates]
    resolved_per_day = [random.randint(1, 6) for _ in dates]
    
    trend_df = pd.DataFrame({
        'Date': dates,
        'Assigned': tickets_per_day,
        'Resolved': resolved_per_day
    })
    
    fig_trend = px.line(
        trend_df, 
        x='Date', 
        y=['Assigned', 'Resolved'],
        title="Daily Ticket Trends"
    )
    fig_trend.update_layout(**chart_theme)
    st.plotly_chart(fig_trend, use_container_width=True)

def main():
    """Enhanced main application entry point for technician dashboard."""
    st.set_page_config(
        page_title=PAGE_TITLE,
        layout=LAYOUT,
        page_icon=PAGE_ICON,
        initial_sidebar_state="expanded"
    )

    # Initialize session state with enhanced defaults
    if "page" not in st.session_state:
        st.session_state.page = "dashboard"
    if "tech_name" not in st.session_state:
        st.session_state.tech_name = "John Smith"
    if "tech_id" not in st.session_state:
        st.session_state.tech_id = "TECH-001"
    if "theme" not in st.session_state:
        st.session_state.theme = "dark"
    if "tech_status" not in st.session_state:
        st.session_state.tech_status = "Available"

    # Apply enhanced custom CSS
    apply_custom_css()

    # Create enhanced sidebar
    create_enhanced_technician_sidebar()

    # Route to appropriate page with enhanced functionality
    try:
        if st.session_state.page == "dashboard":
            dashboard_page()
        elif st.session_state.page == "my_tickets":
            my_tickets_page()
        elif st.session_state.page == "urgent_tickets":
            urgent_tickets_page()
        elif st.session_state.page == "analytics":
            analytics_page()
        else:
            # Default to dashboard if unknown page
            st.session_state.page = "dashboard"
            dashboard_page()
    except Exception as e:
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("🔄 Please refresh the page or contact support if the issue persists.")

if __name__ == "__main__":
    main()

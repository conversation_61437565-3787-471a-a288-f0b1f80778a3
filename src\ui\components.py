"""
Shared UI Components for TeamLogic-AutoTask
Contains reusable Streamlit components and styling functions.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import requests
import json

def apply_custom_css():
    """Apply custom CSS styling with theme support."""
    theme = st.session_state.get('theme', 'dark')
    
    if theme == 'dark':
        theme_vars = """
        :root {
            --primary: #4e73df;
            --primary-hover: #2e59d9;
            --secondary: #181818;
            --accent: #23272f;
            --text-main: #f8f9fa;
            --text-secondary: #b0b3b8;
            --card-bg: #23272f;
            --sidebar-bg: #111111;
            --border-color: #444;
            --input-bg: #23272f;
            --success: #1cc88a;
            --warning: #f6c23e;
            --danger: #e74a3b;
            --info: #36b9cc;
        }
        """
    else:
        theme_vars = """
        :root {
            --primary: #4e73df;
            --primary-hover: #2e59d9;
            --secondary: #ffffff;
            --accent: #f8f9fa;
            --text-main: #212529;
            --text-secondary: #6c757d;
            --card-bg: #ffffff;
            --sidebar-bg: #f8f9fa;
            --border-color: #dee2e6;
            --input-bg: #ffffff;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
            --info: #17a2b8;
        }
        """
    
    st.markdown(f"""
    <style>
    {theme_vars}
    
    .main {{
        background-color: var(--secondary);
        color: var(--text-main);
    }}
    
    .metric-card {{
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }}
    
    .metric-card h3 {{
        color: var(--primary);
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
    }}
    
    .metric-card p {{
        color: var(--text-secondary);
        margin: 0;
        font-size: 1rem;
    }}
    
    .status-badge {{
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-align: center;
        display: inline-block;
        margin: 2px;
    }}
    
    .status-open {{ background-color: var(--info); color: white; }}
    .status-in-progress {{ background-color: var(--warning); color: black; }}
    .status-resolved {{ background-color: var(--success); color: white; }}
    .status-closed {{ background-color: var(--text-secondary); color: white; }}
    .status-assigned {{ background-color: var(--primary); color: white; }}
    .status-pending {{ background-color: var(--warning); color: black; }}
    
    .priority-low {{ color: #28a745; }}
    .priority-medium {{ color: #ffc107; }}
    .priority-high {{ color: #fd7e14; }}
    .priority-critical {{ color: #dc3545; }}
    .priority-desktop {{ color: #e83e8c; }}
    
    .data-table {{
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        border: 1px solid var(--border-color);
    }}
    
    .chart-container {{
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        border: 1px solid var(--border-color);
    }}
    
    .filter-section {{
        background-color: var(--accent);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border: 1px solid var(--border-color);
    }}
    
    .header-section {{
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid var(--border-color);
        border-left: 4px solid var(--primary);
    }}
    
    .footer-section {{
        background-color: var(--accent);
        border-radius: 10px;
        padding: 15px;
        margin-top: 20px;
        border: 1px solid var(--border-color);
        text-align: center;
        color: var(--text-secondary);
    }}
    
    .ticket-card {{
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }}
    
    .ticket-card:hover {{
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }}
    
    .urgent-ticket {{
        border-left: 4px solid var(--danger);
        background-color: rgba(220, 53, 69, 0.1);
    }}
    
    .stButton > button {{
        background-color: var(--primary);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }}
    
    .stButton > button:hover {{
        background-color: var(--primary-hover);
        transform: translateY(-1px);
    }}
    </style>
    """, unsafe_allow_html=True)

def create_metric_card(title: str, value: str, delta: Optional[str] = None, delta_color: str = "normal") -> None:
    """Create a styled metric card."""
    delta_html = ""
    if delta:
        color = "var(--success)" if delta_color == "normal" else "var(--danger)"
        delta_html = f'<p style="color: {color}; font-size: 0.9rem; margin-top: 5px;">{delta}</p>'
    
    st.markdown(f"""
    <div class="metric-card">
        <h3>{value}</h3>
        <p>{title}</p>
        {delta_html}
    </div>
    """, unsafe_allow_html=True)

def create_status_badge(status: str) -> str:
    """Create a styled status badge."""
    status_lower = status.lower().replace(" ", "-")
    return f'<span class="status-badge status-{status_lower}">{status}</span>'

def create_priority_icon(priority: str) -> str:
    """Create priority icon based on priority level."""
    priority_icons = {
        "Low": "🟢",
        "Medium": "🟡", 
        "High": "🟠",
        "Critical": "🔴",
        "Desktop/User Down": "🚨"
    }
    return priority_icons.get(priority, "⚪")

def create_data_table(data: List[Dict], title: str = "Data Table") -> None:
    """Create a styled data table."""
    if not data:
        st.info("No data available")
        return
    
    st.markdown(f"""
    <div class="data-table">
        <h4>{title}</h4>
    </div>
    """, unsafe_allow_html=True)
    
    df = pd.DataFrame(data)
    st.dataframe(df, use_container_width=True)

def create_chart_container(title: str) -> None:
    """Create a styled container for charts."""
    st.markdown(f"""
    <div class="chart-container">
        <h4>{title}</h4>
    </div>
    """, unsafe_allow_html=True)

def create_filter_section(title: str = "Filters") -> None:
    """Create a styled filter section."""
    st.markdown(f"""
    <div class="filter-section">
        <h4>{title}</h4>
    </div>
    """, unsafe_allow_html=True)

def create_header_section(title: str, subtitle: str = "") -> None:
    """Create a styled header section."""
    subtitle_html = f"<p>{subtitle}</p>" if subtitle else ""
    st.markdown(f"""
    <div class="header-section">
        <h2>{title}</h2>
        {subtitle_html}
    </div>
    """, unsafe_allow_html=True)

def create_footer_section(content: str) -> None:
    """Create a styled footer section."""
    st.markdown(f"""
    <div class="footer-section">
        {content}
    </div>
    """, unsafe_allow_html=True)

def format_time_elapsed(created_at: str) -> str:
    """Format time elapsed since ticket creation."""
    try:
        created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        now = datetime.now(created_time.tzinfo) if created_time.tzinfo else datetime.now()
        elapsed = now - created_time
        
        if elapsed.days > 0:
            return f"{elapsed.days} days ago"
        elif elapsed.seconds > 3600:
            hours = elapsed.seconds // 3600
            return f"{hours} hours ago"
        elif elapsed.seconds > 60:
            minutes = elapsed.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"
    except:
        return "Unknown"

def format_date_display(date_str: str) -> str:
    """Format date for display."""
    try:
        date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return date_obj.strftime("%Y-%m-%d %H:%M")
    except:
        return date_str

def get_duration_icon(duration: str) -> str:
    """Get icon for duration filter."""
    icons = {
        "Last hour": "⏰",
        "Last 2 hours": "⏰",
        "Last 6 hours": "⏰",
        "Last 12 hours": "⏰",
        "Today": "📅",
        "Yesterday": "📅",
        "Last 3 days": "📅",
        "Last week": "📅",
        "Last month": "📅",
        "All tickets": "📋"
    }
    return icons.get(duration, "📋")

def api_call(endpoint: str, method: str = "GET", data: Dict = None) -> Dict:
    """Make API call to backend."""
    base_url = "http://localhost:8000"
    url = f"{base_url}/{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        elif method == "PUT":
            response = requests.put(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def display_api_response(response: Dict) -> None:
    """Display API response in a user-friendly way."""
    if response.get("success"):
        st.success("✅ Operation successful")
        if response.get("data"):
            st.json(response["data"])
    else:
        st.error(f"❌ Error: {response.get('error', 'Unknown error')}")

def generate_unique_key(base_key: str, suffix: str = "") -> str:
    """Generate a unique key for Streamlit components."""
    import time
    import random
    timestamp = str(int(time.time() * 1000))
    random_num = str(random.randint(1000, 9999))
    return f"{base_key}_{suffix}_{timestamp}_{random_num}" if suffix else f"{base_key}_{timestamp}_{random_num}"

def create_sidebar():
    """Create a basic sidebar (to be overridden by specific implementations)."""
    with st.sidebar:
        st.markdown("## Navigation")
        st.info("This is a basic sidebar. Override this function in your specific page implementation.")

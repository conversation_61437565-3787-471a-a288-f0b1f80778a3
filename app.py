"""
TeamLogic-AutoTask Application
Direct access to User and Technician dashboards with real Snowflake backend integration.
"""

import warnings
warnings.filterwarnings("ignore", message="You have an incompatible version of 'pyarrow' installed")

import streamlit as st
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import the enhanced UI pages
from src.ui.Pages.User import main as user_main
from src.ui.Pages.Technician import main as technician_main

def main():
    """Main application entry point with role selection."""

    st.set_page_config(
        page_title="TeamLogic-AutoTask",
        layout="wide",
        page_icon="🎫",
        initial_sidebar_state="expanded"
    )

    # Initialize session state
    if "selected_role" not in st.session_state:
        st.session_state.selected_role = None

    # Role selection in sidebar
    with st.sidebar:
        st.markdown("## 🎯 Select Your Role")

        if st.button("👤 User Dashboard", key="select_user", use_container_width=True):
            st.session_state.selected_role = "user"
            st.rerun()

        if st.button("👨‍🔧 Technician Dashboard", key="select_technician", use_container_width=True):
            st.session_state.selected_role = "technician"
            st.rerun()

        if st.session_state.selected_role:
            st.markdown(f"**Current Role:** {st.session_state.selected_role.title()}")

            if st.button("🔄 Switch Role", key="switch_role", use_container_width=True):
                st.session_state.selected_role = None
                st.rerun()

    # Show appropriate dashboard based on selected role
    if st.session_state.selected_role == "user":
        try:
            user_main()
        except Exception as e:
            st.error(f"❌ Error loading User Dashboard: {str(e)}")
            st.info("🔄 Please refresh the page or switch roles.")

    elif st.session_state.selected_role == "technician":
        try:
            technician_main()
        except Exception as e:
            st.error(f"❌ Error loading Technician Dashboard: {str(e)}")
            st.info("🔄 Please refresh the page or switch roles.")

    else:
        # Show role selection page
        st.title("🎫 TeamLogic-AutoTask")
        st.markdown("### Welcome to the Support Ticket Management System")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            <div style="background-color: #f0f8ff; padding: 20px; border-radius: 10px; border-left: 4px solid #4e73df;">
                <h4>👤 User Dashboard</h4>
                <p>Submit new support tickets and track your existing tickets.</p>
                <ul>
                    <li>Submit new tickets</li>
                    <li>Track ticket status</li>
                    <li>View ticket history</li>
                    <li>Get automated resolutions</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div style="background-color: #f0fff4; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">
                <h4>👨‍🔧 Technician Dashboard</h4>
                <p>Manage assigned tickets and update their status.</p>
                <ul>
                    <li>View assigned tickets</li>
                    <li>Update ticket status</li>
                    <li>Change priority levels</li>
                    <li>Add resolution notes</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("---")
        st.info("👆 Please select your role from the sidebar to get started.")

if __name__ == "__main__":
    main()

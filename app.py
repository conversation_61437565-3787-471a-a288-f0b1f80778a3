"""
TeamLogic-AutoTask Application
Enhanced with role-based authentication and improved UI structure.
"""

import warnings
warnings.filterwarnings("ignore", message="You have an incompatible version of 'pyarrow' installed")

import streamlit as st
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import the enhanced UI pages
from src.ui.login import main as login_main, check_authentication, create_logout_button
from src.ui.Pages.User import main as user_main
from src.ui.Pages.Technician import main as technician_main
from src.ui.Pages.Admin import main as admin_main

def main():
    """Enhanced main application entry point with role-based authentication."""

    # Check if user is authenticated
    user_role = check_authentication()

    if user_role:
        # User is authenticated, show appropriate dashboard
        try:
            if user_role == "user":
                # Run enhanced user dashboard
                user_main()
            elif user_role == "technician":
                # Run enhanced technician dashboard
                technician_main()
            elif user_role == "admin":
                # Run enhanced admin dashboard
                admin_main()
            else:
                # Unknown role, redirect to login
                st.error("❌ Unknown user role. Please contact administrator.")
                if st.button("🔄 Return to Login"):
                    # Clear session and redirect to login
                    for key in list(st.session_state.keys()):
                        del st.session_state[key]
                    st.rerun()

        except Exception as e:
            # Handle any errors gracefully
            st.error(f"❌ An error occurred while loading the dashboard: {str(e)}")
            st.info("🔄 Please refresh the page or contact support if the issue persists.")

            # Provide logout option
            if st.button("🚪 Logout and Return to Login"):
                for key in list(st.session_state.keys()):
                    del st.session_state[key]
                st.rerun()

    else:
        # User is not authenticated, show login page
        try:
            login_main()
        except Exception as e:
            st.error(f"❌ Login system error: {str(e)}")
            st.info("🔄 Please refresh the page.")

if __name__ == "__main__":
    main()
